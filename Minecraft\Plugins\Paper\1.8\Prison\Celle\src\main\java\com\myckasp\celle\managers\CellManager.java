package com.myckasp.celle.managers;

import com.myckasp.celle.Celle;
import com.myckasp.celle.models.Cell;
import com.myckasp.celle.models.CellGroup;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public class CellManager {
    
    private final Celle plugin;
    private FileConfiguration cellsConfig;
    private File cellsFile;
    private Map<String, Cell> cells;
    private Map<String, CellGroup> groups;
    
    public CellManager(Celle plugin) {
        this.plugin = plugin;
        this.cells = new HashMap<>();
        this.groups = new HashMap<>();
        setupCellsFile();
        loadCells();
        loadGroups();
    }
    
    private void setupCellsFile() {
        cellsFile = new File(plugin.getDataFolder(), "cells.yml");
        
        if (!cellsFile.exists()) {
            plugin.getLogger().info("Opretter cells.yml fil...");
            try {
                cellsFile.createNewFile();
            } catch (IOException e) {
                plugin.getLogger().severe("Kunne ikke oprette cells.yml fil: " + e.getMessage());
            }
        }
        
        cellsConfig = YamlConfiguration.loadConfiguration(cellsFile);
    }
    
    private void loadCells() {
        if (!cellsConfig.contains("cells")) {
            cellsConfig.createSection("cells");
        }
        
        for (String cellName : cellsConfig.getConfigurationSection("cells").getKeys(false)) {
            Cell cell = loadCellFromConfig(cellName);
            if (cell != null) {
                cells.put(cellName, cell);
            }
        }
    }
    
    private void loadGroups() {
        // Load default group if it doesn't exist
        if (!cellsConfig.contains("groups.default")) {
            CellGroup defaultGroup = new CellGroup("default", "Standard gruppe for celler");
            groups.put("default", defaultGroup);
            saveGroup(defaultGroup);
        } else {
            // Load existing groups
            if (!cellsConfig.contains("groups")) {
                cellsConfig.createSection("groups");
            }
            
            for (String groupName : cellsConfig.getConfigurationSection("groups").getKeys(false)) {
                CellGroup group = loadGroupFromConfig(groupName);
                if (group != null) {
                    groups.put(groupName, group);
                }
            }
        }
    }
    
    private Cell loadCellFromConfig(String cellName) {
        try {
            String worldName = cellsConfig.getString("cells." + cellName + ".world");
            double minX = cellsConfig.getDouble("cells." + cellName + ".minX");
            double minY = cellsConfig.getDouble("cells." + cellName + ".minY");
            double minZ = cellsConfig.getDouble("cells." + cellName + ".minZ");
            double maxX = cellsConfig.getDouble("cells." + cellName + ".maxX");
            double maxY = cellsConfig.getDouble("cells." + cellName + ".maxY");
            double maxZ = cellsConfig.getDouble("cells." + cellName + ".maxZ");
            
            UUID ownerUUID = UUID.fromString(cellsConfig.getString("cells." + cellName + ".owner"));
            String groupName = cellsConfig.getString("cells." + cellName + ".group", "default");
            
            // Load rental properties
            boolean forRent = cellsConfig.getBoolean("cells." + cellName + ".forRent", false);
            double rentPrice = cellsConfig.getDouble("cells." + cellName + ".rentPrice", 350.0);
            int maxRentDays = cellsConfig.getInt("cells." + cellName + ".maxRentDays", 10);
            int defaultRentDays = cellsConfig.getInt("cells." + cellName + ".defaultRentDays", 1);
            
            World world = plugin.getServer().getWorld(worldName);
            if (world == null) {
                plugin.getLogger().warning("Verden '" + worldName + "' for cellen '" + cellName + "' blev ikke fundet!");
                return null;
            }
            
            Location min = new Location(world, minX, minY, minZ);
            Location max = new Location(world, maxX, maxY, maxZ);
            
            Cell cell = new Cell(cellName, min, max, ownerUUID, groupName);
            cell.setForRent(forRent);
            cell.setRentPrice(rentPrice);
            cell.setMaxRentDays(maxRentDays);
            cell.setDefaultRentDays(defaultRentDays);
            
            return cell;
        } catch (Exception e) {
            plugin.getLogger().severe("Fejl ved indlæsning af cellen '" + cellName + "': " + e.getMessage());
            return null;
        }
    }
    
    private CellGroup loadGroupFromConfig(String groupName) {
        try {
            String description = cellsConfig.getString("groups." + groupName + ".description", "Ingen beskrivelse");
            double defaultRentPrice = cellsConfig.getDouble("groups." + groupName + ".defaultRentPrice", 350.0);
            
            CellGroup group = new CellGroup(groupName, description);
            group.setDefaultRentPrice(defaultRentPrice);
            return group;
        } catch (Exception e) {
            plugin.getLogger().severe("Fejl ved indlæsning af gruppen '" + groupName + "': " + e.getMessage());
            return null;
        }
    }
    
    public Cell createCell(String name, Location min, Location max, UUID owner, String groupName) {
        Cell cell = new Cell(name, min, max, owner, groupName);
        cells.put(name, cell);
        saveCell(cell);
        return cell;
    }
    
    public void saveCell(Cell cell) {
        cellsConfig.set("cells." + cell.getName() + ".world", cell.getMin().getWorld().getName());
        cellsConfig.set("cells." + cell.getName() + ".minX", cell.getMin().getX());
        cellsConfig.set("cells." + cell.getName() + ".minY", cell.getMin().getY());
        cellsConfig.set("cells." + cell.getName() + ".minZ", cell.getMin().getZ());
        cellsConfig.set("cells." + cell.getName() + ".maxX", cell.getMax().getX());
        cellsConfig.set("cells." + cell.getName() + ".maxY", cell.getMax().getY());
        cellsConfig.set("cells." + cell.getName() + ".maxZ", cell.getMax().getZ());
        cellsConfig.set("cells." + cell.getName() + ".owner", cell.getOwner().toString());
        cellsConfig.set("cells." + cell.getName() + ".group", cell.getGroupName());
        
        // Save rental properties
        cellsConfig.set("cells." + cell.getName() + ".forRent", cell.isForRent());
        cellsConfig.set("cells." + cell.getName() + ".rentPrice", cell.getRentPrice());
        cellsConfig.set("cells." + cell.getName() + ".maxRentDays", cell.getMaxRentDays());
        cellsConfig.set("cells." + cell.getName() + ".defaultRentDays", cell.getDefaultRentDays());
        
        saveCellsConfig();
    }
    
    public void removeCell(String name) {
        cells.remove(name);
        cellsConfig.set("cells." + name, null);
        saveCellsConfig();
    }
    
    public Cell getCell(String name) {
        return cells.get(name);
    }
    
    public List<Cell> getAllCells() {
        return new ArrayList<>(cells.values());
    }
    
    public List<Cell> getCellsByGroup(String groupName) {
        List<Cell> groupCells = new ArrayList<>();
        for (Cell cell : cells.values()) {
            if (cell.getGroupName().equalsIgnoreCase(groupName)) {
                groupCells.add(cell);
            }
        }
        return groupCells;
    }
    
    public List<Cell> getCellsByOwner(UUID owner) {
        List<Cell> ownerCells = new ArrayList<>();
        for (Cell cell : cells.values()) {
            if (cell.getOwner().equals(owner)) {
                ownerCells.add(cell);
            }
        }
        return ownerCells;
    }
    
    public boolean cellExists(String name) {
        return cells.containsKey(name);
    }
    
    public CellGroup createGroup(String name, String description) {
        CellGroup group = new CellGroup(name, description);
        groups.put(name, group);
        saveGroup(group);
        return group;
    }
    
    public void saveGroup(CellGroup group) {
        cellsConfig.set("groups." + group.getName() + ".description", group.getDescription());
        cellsConfig.set("groups." + group.getName() + ".defaultRentPrice", group.getDefaultRentPrice());
        saveCellsConfig();
    }
    
    public CellGroup getGroup(String name) {
        return groups.get(name);
    }
    
    public List<CellGroup> getAllGroups() {
        return new ArrayList<>(groups.values());
    }
    
    public boolean groupExists(String name) {
        return groups.containsKey(name);
    }
    
    private void saveCellsConfig() {
        try {
            cellsConfig.save(cellsFile);
        } catch (IOException e) {
            plugin.getLogger().severe("Kunne ikke gemme cells.yml fil: " + e.getMessage());
        }
    }
    
    public void reloadCells() {
        cells.clear();
        groups.clear();
        setupCellsFile();
        loadCells();
        loadGroups();
    }
}