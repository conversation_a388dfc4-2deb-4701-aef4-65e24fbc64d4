package com.myckasp.celle.models;

public class CellGroup {
    
    private String name;
    private String description;
    private double defaultRentPrice;
    
    public CellGroup(String name, String description) {
        this.name = name;
        this.description = description;
        // Set default price based on group name
        if ("default".equalsIgnoreCase(name)) {
            this.defaultRentPrice = 350.0; // Default price for default group
        } else {
            this.defaultRentPrice = 350.0; // Default for other groups too for now
        }
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public double getDefaultRentPrice() {
        return defaultRentPrice;
    }
    
    public void setDefaultRentPrice(double defaultRentPrice) {
        this.defaultRentPrice = defaultRentPrice;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        CellGroup cellGroup = (CellGroup) obj;
        return name.equals(cellGroup.name);
    }
    
    @Override
    public int hashCode() {
        return name.hashCode();
    }
    
    @Override
    public String toString() {
        return "CellGroup{name='" + name + "', description='" + description + "'}";
    }
}