package com.myckasp.celle.config;

import com.myckasp.celle.Celle;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class LanguageManager {
    
    private final Celle plugin;
    private FileConfiguration langConfig;
    private File langFile;
    private String currentLanguage;
    
    // Danish language messages
    private final Map<String, String> danishMessages = new HashMap<>();
    
    public LanguageManager(Celle plugin) {
        this.plugin = plugin;
        setupLanguage();
        setupDefaultMessages();
    }
    
    private void setupLanguage() {
        langFile = new File(plugin.getDataFolder(), "language.yml");
        
        if (!langFile.exists()) {
            plugin.getLogger().info("Opretter sprogfil...");
            createDefaultLanguageFile();
        }
        
        langConfig = YamlConfiguration.loadConfiguration(langFile);
        currentLanguage = langConfig.getString("language", "danish");
    }
    
    private void createDefaultLanguageFile() {
        langConfig = YamlConfiguration.loadConfiguration(langFile);
        langConfig.set("language", "danish");
        
        // Command messages
        langConfig.set("messages.command.no-permission", "&cDu har ikke tilladelse til at bruge denne kommando!");
        langConfig.set("messages.command.invalid-syntax", "&cUgyldig syntaks! Brug: /celle <add|remove|list|info>");
        langConfig.set("messages.command.cell-added", "&aCellen '%cellname%' er blevet tilføjet til gruppen '%group%'!");
        langConfig.set("messages.command.cell-not-found", "&cCellen '%cellname%' blev ikke fundet!");
        langConfig.set("messages.command.cell-removed", "&aCellen '%cellname%' er blevet fjernet!");
        langConfig.set("messages.command.list-header", "&eCeller i gruppen '%group%':");
        langConfig.set("messages.command.list-entry", "&7- %cellname% (ejer: %owner%)");
        langConfig.set("messages.command.cell-info", "&eInformation om cellen '%cellname%':");
        langConfig.set("messages.command.cell-info-owner", "&7Ejer: %owner%");
        langConfig.set("messages.command.cell-info-group", "&7Gruppe: %group%");
        langConfig.set("messages.command.cell-info-location", "&7Position: %world%, %x%, %y%, %z%");
        
        // Error messages
        langConfig.set("messages.errors.already-in-cell", "&cDu er allerede i en celle!");
        langConfig.set("messages.errors.no-selection", "&cDu skal markere et område med WorldEdit først!");
        langConfig.set("messages.errors.world-not-enabled", "&cWorldGuard er ikke aktiveret på denne verden!");
        langConfig.set("messages.errors.cell-exists", "&cCellen '%cellname%' eksisterer allerede!");
        langConfig.set("messages.errors.unknown-group", "&cGruppen '%group%' eksisterer ikke!");
        
        // Rental messages
        langConfig.set("messages.rental.cell-not-for-rent", "&cDenne celle kan ikke leases!");
        langConfig.set("messages.rental.cell-rented", "&aDu har lejet cellen '%cellname%' for %days% dage for %price% kr.");
        langConfig.set("messages.rental.cell-already-owned", "&cDu ejer allerede denne celle!");
        langConfig.set("messages.rental.sign-set", "&aSkiltet er blevet opsat for cellen '%cellname%'!");
        langConfig.set("messages.rental.invalid-sign", "&cDu skal kigge på et skilt!");
        langConfig.set("messages.rental.rent-cost", "&eLejepris: %price% kr./dag");
        langConfig.set("messages.rental.max-rent-days", "&eMaks lejeperiode: %days% dage");
        
        try {
            langConfig.save(langFile);
        } catch (IOException e) {
            plugin.getLogger().severe("Kunne ikke gemme sprogfil: " + e.getMessage());
        }
    }
    
    private void setupDefaultMessages() {
        danishMessages.put("command.no-permission", "&cDu har ikke tilladelse til at bruge denne kommando!");
        danishMessages.put("command.invalid-syntax", "&cUgyldig syntaks! Brug: /celle <add|remove|list|info>");
        danishMessages.put("command.cell-added", "&aCellen '%cellname%' er blevet tilføjet til gruppen '%group%'!");
        danishMessages.put("command.cell-not-found", "&cCellen '%cellname%' blev ikke fundet!");
        danishMessages.put("command.cell-removed", "&aCellen '%cellname%' er blevet fjernet!");
        danishMessages.put("command.list-header", "&eCeller i gruppen '%group%':");
        danishMessages.put("command.list-entry", "&7- %cellname% (ejer: %owner%)");
        danishMessages.put("command.cell-info", "&eInformation om cellen '%cellname%':");
        danishMessages.put("command.cell-info-owner", "&7Ejer: %owner%");
        danishMessages.put("command.cell-info-group", "&7Gruppe: %group%");
        danishMessages.put("command.cell-info-location", "&7Position: %world%, %x%, %y%, %z%");
        danishMessages.put("errors.already-in-cell", "&cDu er allerede i en celle!");
        danishMessages.put("errors.no-selection", "&cDu skal markere et område med WorldEdit først!");
        danishMessages.put("errors.world-not-enabled", "&cWorldGuard er ikke aktiveret på denne verden!");
        danishMessages.put("errors.cell-exists", "&cCellen '%cellname%' eksisterer allerede!");
        danishMessages.put("errors.unknown-group", "&cGruppen '%group%' eksisterer ikke!");
        
        // Rental messages
        danishMessages.put("rental.cell-not-for-rent", "&cDenne celle kan ikke leases!");
        danishMessages.put("rental.cell-rented", "&aDu har lejet cellen '%cellname%' for %days% dage for %price% kr.");
        danishMessages.put("rental.cell-already-owned", "&cDu ejer allerede denne celle!");
        danishMessages.put("rental.sign-set", "&aSkiltet er blevet opsat for cellen '%cellname%'!");
        danishMessages.put("rental.invalid-sign", "&cDu skal kigge på et skilt!");
        danishMessages.put("rental.rent-cost", "&eLejepris: %price% kr./dag");
        danishMessages.put("rental.max-rent-days", "&eMaks lejeperiode: %days% dage");
    }
    
    public String getMessage(String key) {
        return langConfig.getString("messages." + key, danishMessages.getOrDefault(key, "&cMelding ikke fundet: " + key));
    }
    
    public String getMessage(String key, String... replacements) {
        String message = getMessage(key);
        
        for (int i = 0; i < replacements.length; i++) {
            message = message.replace("%" + (i + 1) + "%", replacements[i]);
        }
        
        return message;
    }
    
    public String getMessage(String key, Map<String, String> replacements) {
        String message = getMessage(key);
        
        for (Map.Entry<String, String> entry : replacements.entrySet()) {
            message = message.replace(entry.getKey(), entry.getValue());
        }
        
        return message;
    }
    
    public void reloadLanguage() {
        try {
            langConfig.load(langFile);
            currentLanguage = langConfig.getString("language", "danish");
        } catch (IOException e) {
            plugin.getLogger().severe("Kunne ikke indlæse sprogfil: " + e.getMessage());
        } catch (Exception e) {
            plugin.getLogger().severe("Fejl ved indlæsning af sprogfil: " + e.getMessage());
        }
    }
    
    public String getCurrentLanguage() {
        return currentLanguage;
    }
}