package com.myckasp.celle;

import com.myckasp.celle.commands.CelleCommand;
import com.myckasp.celle.config.ConfigManager;
import com.myckasp.celle.config.LanguageManager;
import com.myckasp.celle.listeners.SignInteractListener;
import com.myckasp.celle.managers.CellManager;
import com.myckasp.celle.utils.WorldGuardUtils;
import org.bukkit.plugin.java.JavaPlugin;

public class Celle extends JavaPlugin {
    
    private static Celle instance;
    private ConfigManager configManager;
    private LanguageManager languageManager;
    private CellManager cellManager;
    private WorldGuardUtils worldGuardUtils;
    
    @Override
    public void onEnable() {
        instance = this;
        
        // Initialize managers
        configManager = new ConfigManager(this);
        languageManager = new LanguageManager(this);
        cellManager = new CellManager(this);
        worldGuardUtils = new WorldGuardUtils(this);
        
        // Register commands
        getCommand("celle").setExecutor(new CelleCommand(this));
        
        // Register events
        getServer().getPluginManager().registerEvents(new SignInteractListener(this), this);
        
        getLogger().info("Celle plugin er blevet aktiveret!");
    }
    
    @Override
    public void onDisable() {
        getLogger().info("Celle plugin er blevet deaktiveret!");
    }
    
    public static Celle getInstance() {
        return instance;
    }
    
    public ConfigManager getConfigManager() {
        return configManager;
    }
    
    public LanguageManager getLanguageManager() {
        return languageManager;
    }
    
    public CellManager getCellManager() {
        return cellManager;
    }
    
    public WorldGuardUtils getWorldGuardUtils() {
        return worldGuardUtils;
    }
}