package com.myckasp.celle.commands;

import com.myckasp.celle.Celle;
import com.myckasp.celle.managers.CellManager;
import com.myckasp.celle.models.Cell;
import com.myckasp.celle.utils.WorldGuardUtils;
import org.bukkit.Location;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.UUID;

public class CelleCommand implements CommandExecutor {
    
    private final Celle plugin;
    
    public CelleCommand(Celle plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("Denne kommando kan kun bruges af spillere!");
            return true;
        }
        
        Player player = (Player) sender;
        
        if (!player.hasPermission("celle.admin")) {
            player.sendMessage(plugin.getLanguageManager().getMessage("command.no-permission"));
            return true;
        }
        
        if (args.length == 0) {
            sendHelp(player);
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "add":
                handleAddCommand(player, args);
                break;
            case "remove":
                handleRemoveCommand(player, args);
                break;
            case "list":
                handleListCommand(player, args);
                break;
            case "info":
                handleInfoCommand(player, args);
                break;
            case "rent":
                handleRentCommand(player, args);
                break;
            case "setsign":
                handleSetSignCommand(player, args);
                break;
            default:
                sendHelp(player);
                break;
        }
        
        return true;
    }
    
    private void sendHelp(Player player) {
        player.sendMessage("§e=== Celle Kommandoer ===");
        player.sendMessage("§6/celle add <cellname> [group] §7- Tilføj en ny celle");
        player.sendMessage("§6/celle remove <cellname> §7- Fjern en celle");
        player.sendMessage("§6/celle list [group] §7- List alle celler");
        player.sendMessage("§6/celle info <cellname> §7- Se information om en celle");
        player.sendMessage("§6/celle rent <cellname> [days] §7- Lej en celle");
        player.sendMessage("§6/celle setsign <cellname> §7- Opsæt et lejeskilte for en celle");
    }
    
    private void handleAddCommand(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage("§cUgyldig syntaks! Brug: /celle add <cellname> [group]");
            return;
        }
        
        String cellName = args[1];
        String groupName = args.length > 2 ? args[2] : "default";
        
        CellManager cellManager = plugin.getCellManager();
        WorldGuardUtils worldGuardUtils = plugin.getWorldGuardUtils();
        
        // Check if cell already exists
        if (cellManager.cellExists(cellName)) {
            player.sendMessage(plugin.getLanguageManager().getMessage("errors.cell-exists", cellName));
            return;
        }
        
        // Check if group exists
        if (!cellManager.groupExists(groupName)) {
            player.sendMessage(plugin.getLanguageManager().getMessage("errors.unknown-group", groupName));
            return;
        }
        
        // Check if player has WorldEdit selection
        if (!worldGuardUtils.hasSelection(player)) {
            player.sendMessage(plugin.getLanguageManager().getMessage("errors.no-selection"));
            return;
        }
        
        // Get selection
        Location[] selection = worldGuardUtils.getSelection(player);
        if (selection == null || selection.length != 2) {
            player.sendMessage(plugin.getLanguageManager().getMessage("errors.no-selection"));
            return;
        }
        
        Location min = selection[0];
        Location max = selection[1];
        
        // Create WorldGuard region
        if (!worldGuardUtils.createProtectedRegion(player, cellName, min, max)) {
            player.sendMessage("§cKunne ikke oprette WorldGuard region!");
            return;
        }
        
        // Create cell
        UUID ownerUUID = player.getUniqueId();
        cellManager.createCell(cellName, min, max, ownerUUID, groupName);
        
        player.sendMessage(plugin.getLanguageManager().getMessage("command.cell-added", cellName, groupName));
    }
    
    private void handleRemoveCommand(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage("§cUgyldig syntaks! Brug: /celle remove <cellname>");
            return;
        }
        
        String cellName = args[1];
        CellManager cellManager = plugin.getCellManager();
        WorldGuardUtils worldGuardUtils = plugin.getWorldGuardUtils();
        
        // Check if cell exists
        if (!cellManager.cellExists(cellName)) {
            player.sendMessage(plugin.getLanguageManager().getMessage("command.cell-not-found", cellName));
            return;
        }
        
        Cell cell = cellManager.getCell(cellName);
        
        // Check if player is owner or admin
        if (!cell.isOwner(player) && !player.hasPermission("celle.admin")) {
            player.sendMessage("§cDu ejer ikke denne celle!");
            return;
        }
        
        // Remove WorldGuard region
        worldGuardUtils.deleteProtectedRegion(player, cellName);
        
        // Remove cell
        cellManager.removeCell(cellName);
        
        player.sendMessage(plugin.getLanguageManager().getMessage("command.cell-removed", cellName));
    }
    
    private void handleListCommand(Player player, String[] args) {
        String groupName = args.length > 1 ? args[1] : null;
        CellManager cellManager = plugin.getCellManager();
        
        if (groupName == null) {
            // List all cells
            player.sendMessage("§e=== Alle Celler ===");
            for (Cell cell : cellManager.getAllCells()) {
                String ownerName = plugin.getServer().getOfflinePlayer(cell.getOwner()).getName();
                player.sendMessage("§7- " + cell.getName() + " (ejer: " + ownerName + ", gruppe: " + cell.getGroupName() + ")");
            }
        } else {
            // List cells in specific group
            if (!cellManager.groupExists(groupName)) {
                player.sendMessage(plugin.getLanguageManager().getMessage("errors.unknown-group", groupName));
                return;
            }
            
            player.sendMessage(plugin.getLanguageManager().getMessage("command.list-header", groupName));
            for (Cell cell : cellManager.getCellsByGroup(groupName)) {
                String ownerName = plugin.getServer().getOfflinePlayer(cell.getOwner()).getName();
                player.sendMessage(plugin.getLanguageManager().getMessage("command.list-entry", cell.getName(), ownerName));
            }
        }
    }
    
    private void handleInfoCommand(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage("§cUgyldig syntaks! Brug: /celle info <cellname>");
            return;
        }
        
        String cellName = args[1];
        CellManager cellManager = plugin.getCellManager();
        
        // Check if cell exists
        if (!cellManager.cellExists(cellName)) {
            player.sendMessage(plugin.getLanguageManager().getMessage("command.cell-not-found", cellName));
            return;
        }
        
        Cell cell = cellManager.getCell(cellName);
        String ownerName = plugin.getServer().getOfflinePlayer(cell.getOwner()).getName();
        
        player.sendMessage(plugin.getLanguageManager().getMessage("command.cell-info", cellName));
        player.sendMessage(plugin.getLanguageManager().getMessage("command.cell-info-owner", ownerName));
        player.sendMessage(plugin.getLanguageManager().getMessage("command.cell-info-group", cell.getGroupName()));
        player.sendMessage(plugin.getLanguageManager().getMessage("command.cell-info-location",
            cell.getWorld().getName(),
            String.valueOf((int) cell.getCenterX()),
            String.valueOf((int) cell.getCenterY()),
            String.valueOf((int) cell.getCenterZ())));
    }
    
    private void handleRentCommand(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage("§cUgyldig syntaks! Brug: /celle rent <cellname> [days]");
            return;
        }
        
        String cellName = args[1];
        CellManager cellManager = plugin.getCellManager();
        
        // Check if cell exists
        if (!cellManager.cellExists(cellName)) {
            player.sendMessage(plugin.getLanguageManager().getMessage("command.cell-not-found", cellName));
            return;
        }
        
        Cell cell = cellManager.getCell(cellName);
        
        // Check if cell is for rent
        if (!cell.isForRent()) {
            player.sendMessage(plugin.getLanguageManager().getMessage("rental.cell-not-for-rent"));
            return;
        }
        
        // Check if player is already in a cell
        // This would need to be implemented with a check for current rented cell
        
        // Get number of days (default to cell's default)
        int days = cell.getDefaultRentDays();
        if (args.length > 2) {
            try {
                days = Integer.parseInt(args[2]);
                if (days < 1 || days > cell.getMaxRentDays()) {
                    player.sendMessage("§cAntal dage skal være mellem 1 og " + cell.getMaxRentDays() + "!");
                    return;
                }
            } catch (NumberFormatException e) {
                player.sendMessage("§cUgyldigt antal dage! Brug et heltal.");
                return;
            }
        }
        
        // Calculate total cost
        double totalCost = cell.getRentPrice() * days;
        
        // Check if player has enough money (would need economy plugin integration)
        // For now, just show the cost and proceed
        
        // Set the player as the renter (for now, just setting owner)
        // In a real implementation, this would be a separate renter field
        cell.setOwner(player.getUniqueId());
        cellManager.saveCell(cell);
        
        player.sendMessage(plugin.getLanguageManager().getMessage("rental.cell-rented", cellName, String.valueOf(days), String.valueOf(totalCost)));
    }
    
    private void handleSetSignCommand(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage("§cUgyldig syntaks! Brug: /celle setsign <cellname>");
            return;
        }
        
        String cellName = args[1];
        CellManager cellManager = plugin.getCellManager();
        
        // Check if cell exists
        if (!cellManager.cellExists(cellName)) {
            player.sendMessage(plugin.getLanguageManager().getMessage("command.cell-not-found", cellName));
            return;
        }
        
        Cell cell = cellManager.getCell(cellName);
        
        // Check if cell is for rent
        if (!cell.isForRent()) {
            player.sendMessage(plugin.getLanguageManager().getMessage("rental.cell-not-for-rent"));
            return;
        }
        
        // Get the block the player is looking at
        org.bukkit.block.Block block = player.getTargetBlock((java.util.Set<org.bukkit.Material>) null, 5);
        
        // Check if it's a sign
        if (!(block.getState() instanceof org.bukkit.block.Sign)) {
            player.sendMessage(plugin.getLanguageManager().getMessage("rental.invalid-sign"));
            return;
        }
        
        org.bukkit.block.Sign sign = (org.bukkit.block.Sign) block.getState();
        
        // Set the sign text
        // First line: "Leje" in Danish
        sign.setLine(0, "Leje");
        
        // Second line: Cell name
        sign.setLine(1, cellName);
        
        // Third line: Price per day
        sign.setLine(2, cell.getRentPrice() + " kr./dag");
        
        // Fourth line: Max days
        sign.setLine(3, "Max " + cell.getMaxRentDays() + " dage");
        
        // Update the sign
        sign.update();
        
        player.sendMessage(plugin.getLanguageManager().getMessage("rental.sign-set", cellName));
    }
}