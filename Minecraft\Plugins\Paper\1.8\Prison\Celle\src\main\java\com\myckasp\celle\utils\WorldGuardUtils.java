package com.myckasp.celle.utils;

import com.sk89q.worldedit.LocalSession;
import com.sk89q.worldedit.Vector;
import com.sk89q.worldedit.WorldEdit;
import com.sk89q.worldedit.bukkit.WorldEditPlugin;
import com.sk89q.worldedit.entity.Player;
import com.sk89q.worldedit.regions.Region;
import com.sk89q.worldguard.bukkit.WorldGuardPlugin;
import com.sk89q.worldguard.protection.managers.RegionManager;
import com.sk89q.worldguard.protection.regions.ProtectedCuboidRegion;
import com.sk89q.worldguard.protection.regions.ProtectedRegion;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.entity.Player as BukkitPlayer;

import java.util.ArrayList;
import java.util.List;

public class WorldGuardUtils {
    
    private final com.myckasp.celle.Celle plugin;
    
    public WorldGuardUtils(com.myckasp.celle.Celle plugin) {
        this.plugin = plugin;
    }
    
    public boolean isWorldGuardEnabled() {
        return plugin.getServer().getPluginManager().isPluginEnabled("WorldGuard");
    }
    
    public boolean isWorldEditEnabled() {
        return plugin.getServer().getPluginManager().isPluginEnabled("WorldEdit");
    }
    
    // WorldEdit integration methods
    public boolean hasSelection(BukkitPlayer player) {
        try {
            // Check if WorldEdit is available
            if (!isWorldEditEnabled()) {
                plugin.getLogger().info("WorldEdit is not enabled");
                return false;
            }
            
            // Get WorldEdit plugin
            WorldEditPlugin wePlugin = (WorldEditPlugin) plugin.getServer().getPluginManager().getPlugin("WorldEdit");
            if (wePlugin == null) {
                plugin.getLogger().info("WorldEdit plugin not found");
                return false;
            }
            
            // Get WorldEdit player wrapper
            Player wePlayer = wePlugin.wrapPlayer(player);
            if (wePlayer == null) {
                plugin.getLogger().info("Could not wrap player for WorldEdit");
                return false;
            }
            
            // Get LocalSession for the player
            LocalSession session = wePlayer.getSession();
            if (session == null) {
                plugin.getLogger().info("Could not get LocalSession");
                return false;
            }
            
            // Get the selection for the player's world
            Region selection = session.getSelection(wePlayer.getWorld());
            
            boolean hasSelection = selection != null;
            plugin.getLogger().info("Player has WorldEdit selection: " + hasSelection);
            return hasSelection;
            
        } catch (Exception e) {
            plugin.getLogger().warning("Fejl ved tjek af WorldEdit valg: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    public Location[] getSelection(BukkitPlayer player) {
        try {
            // Check if WorldEdit is available
            if (!isWorldEditEnabled()) {
                return null;
            }
            
            // Get WorldEdit plugin
            WorldEditPlugin wePlugin = (WorldEditPlugin) plugin.getServer().getPluginManager().getPlugin("WorldEdit");
            if (wePlugin == null) {
                return null;
            }
            
            // Get WorldEdit player wrapper
            Player wePlayer = wePlugin.wrapPlayer(player);
            if (wePlayer == null) {
                return null;
            }
            
            // Get LocalSession for the player
            LocalSession session = wePlayer.getSession();
            if (session == null) {
                return null;
            }
            
            // Get the selection for the player's world
            Region selection = session.getSelection(wePlayer.getWorld());
            if (selection == null) {
                return null;
            }
            
            // Get the points of the selection
            Vector point1 = selection.getMinimumPoint();
            Vector point2 = selection.getMaximumPoint();
            
            // Convert to Bukkit locations
            double x1 = point1.getX();
            double y1 = point1.getY();
            double z1 = point1.getZ();
            
            double x2 = point2.getX();
            double y2 = point2.getY();
            double z2 = point2.getZ();
            
            Location min = new Location(player.getWorld(), Math.min(x1, x2), Math.min(y1, y2), Math.min(z1, z2));
            Location max = new Location(player.getWorld(), Math.max(x1, x2), Math.max(y1, y2), Math.max(z1, z2));
            
            return new Location[]{min, max};
            
        } catch (Exception e) {
            plugin.getLogger().warning("Fejl ved hentning af WorldEdit valg: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    public boolean createProtectedRegion(BukkitPlayer player, String regionName, Location min, Location max) {
        try {
            // Check if WorldGuard is available
            if (!isWorldGuardEnabled()) {
                plugin.getLogger().info("WorldGuard is not enabled");
                return false;
            }
            
            // Get WorldGuard plugin
            WorldGuardPlugin wgPlugin = (WorldGuardPlugin) plugin.getServer().getPluginManager().getPlugin("WorldGuard");
            if (wgPlugin == null) {
                plugin.getLogger().info("WorldGuard plugin not found");
                return false;
            }
            
            // Get the region manager for the player's world
            RegionManager regionManager = wgPlugin.getRegionContainer().get(min.getWorld());
            if (regionManager == null) {
                plugin.getLogger().info("Could not get region manager for world: " + min.getWorld().getName());
                return false;
            }
            
            // Create vectors for the region
            Vector vector1 = new Vector(min.getX(), min.getY(), min.getZ());
            Vector vector2 = new Vector(max.getX(), max.getY(), max.getZ());
            
            // Create the protected region
            ProtectedCuboidRegion protectedRegion = new ProtectedCuboidRegion(regionName, vector1, vector2);
            
            // Add the region
            regionManager.addRegion(protectedRegion);
            
            plugin.getLogger().info("Successfully created WorldGuard region: " + regionName);
            return true;
            
        } catch (Exception e) {
            plugin.getLogger().warning("Fejl ved oprettelse af WorldGuard region: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    public boolean deleteProtectedRegion(BukkitPlayer player, String regionName) {
        try {
            // Check if WorldGuard is available
            if (!isWorldGuardEnabled()) {
                return false;
            }
            
            // Get WorldGuard plugin
            WorldGuardPlugin wgPlugin = (WorldGuardPlugin) plugin.getServer().getPluginManager().getPlugin("WorldGuard");
            if (wgPlugin == null) {
                return false;
            }
            
            // Get the region manager for the player's world
            RegionManager regionManager = wgPlugin.getRegionContainer().get(player.getWorld());
            if (regionManager == null) {
                return false;
            }
            
            // Remove the region
            regionManager.removeRegion(regionName);
            
            return true;
            
        } catch (Exception e) {
            plugin.getLogger().warning("Fejl ved sletning af WorldGuard region: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    public List<String> getRegionsAtLocation(Location location) {
        try {
            if (!isWorldGuardEnabled()) {
                return new ArrayList<>();
            }
            
            WorldGuardPlugin wgPlugin = (WorldGuardPlugin) plugin.getServer().getPluginManager().getPlugin("WorldGuard");
            if (wgPlugin == null) {
                return new ArrayList<>();
            }
            
            RegionManager regionManager = wgPlugin.getRegionContainer().get(location.getWorld());
            if (regionManager == null) {
                return new ArrayList<>();
            }
            
            Vector vector = new Vector(location.getX(), location.getY(), location.getZ());
            List<String> regions = new ArrayList<>();
            
            for (ProtectedRegion region : regionManager.getRegions().values()) {
                if (region.contains(vector)) {
                    regions.add(region.getId());
                }
            }
            
            return regions;
            
        } catch (Exception e) {
            plugin.getLogger().warning("Fejl ved hentning af regioner: " + e.getMessage());
            return new ArrayList<>();
        }
    }
    
    public boolean isLocationInRegion(Location location, String regionName) {
        try {
            if (!isWorldGuardEnabled()) {
                return false;
            }
            
            WorldGuardPlugin wgPlugin = (WorldGuardPlugin) plugin.getServer().getPluginManager().getPlugin("WorldGuard");
            if (wgPlugin == null) {
                return false;
            }
            
            RegionManager regionManager = wgPlugin.getRegionContainer().get(location.getWorld());
            if (regionManager == null) {
                return false;
            }
            
            ProtectedRegion region = regionManager.getRegion(regionName);
            if (region == null) {
                return false;
            }
            
            Vector vector = new Vector(location.getX(), location.getY(), location.getZ());
            return region.contains(vector);
            
        } catch (Exception e) {
            plugin.getLogger().warning("Fejl ved tjek af region: " + e.getMessage());
            return false;
        }
    }
    
    public boolean playerCanBuildInRegion(BukkitPlayer player, String regionName) {
        try {
            if (!isWorldGuardEnabled()) {
                return true; // If WorldGuard is not available, allow building
            }
            
            WorldGuardPlugin wgPlugin = (WorldGuardPlugin) plugin.getServer().getPluginManager().getPlugin("WorldGuard");
            if (wgPlugin == null) {
                return true;
            }
            
            RegionManager regionManager = wgPlugin.getRegionContainer().get(player.getWorld());
            if (regionManager == null) {
                return true;
            }
            
            ProtectedRegion region = regionManager.getRegion(regionName);
            if (region == null) {
                return true;
            }
            
            // Check if player can build in the region
            return region.isMember(player.getUniqueId()) || region.isOwner(player.getUniqueId());
            
        } catch (Exception e) {
            plugin.getLogger().warning("Fejl ved tjek af byggetilladelse: " + e.getMessage());
            return true;
        }
    }
}