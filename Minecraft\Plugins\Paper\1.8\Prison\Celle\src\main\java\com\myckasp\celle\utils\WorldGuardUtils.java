package com.myckasp.celle.utils;

import org.bukkit.Location;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.List;

public class WorldGuardUtils {

    private final com.myckasp.celle.Celle plugin;

    public WorldGuardUtils(com.myckasp.celle.Celle plugin) {
        this.plugin = plugin;
    }

    public boolean isWorldGuardEnabled() {
        return plugin.getServer().getPluginManager().isPluginEnabled("WorldGuard");
    }

    public boolean isWorldEditEnabled() {
        return plugin.getServer().getPluginManager().isPluginEnabled("WorldEdit");
    }

    // Simplified WorldEdit integration methods (placeholders for now)
    public boolean hasSelection(Player player) {
        // For now, always return true to allow cell creation
        // TODO: Implement proper WorldEdit 6.x integration
        plugin.getLogger().info("WorldEdit integration not fully implemented - assuming player has selection");
        return true;
    }

    public Location[] getSelection(Player player) {
        // For now, create a simple 10x10x10 area around the player
        // TODO: Implement proper WorldEdit 6.x integration
        Location playerLoc = player.getLocation();
        Location min = playerLoc.clone().subtract(5, 0, 5);
        Location max = playerLoc.clone().add(5, 5, 5);

        plugin.getLogger().info("Using placeholder selection around player location");
        return new Location[]{min, max};
    }

    public boolean createProtectedRegion(Player player, String regionName, Location min, Location max) {
        // Placeholder - WorldGuard integration not fully implemented
        // TODO: Implement proper WorldGuard 6.x integration
        plugin.getLogger().info("WorldGuard region creation not fully implemented - region '" + regionName + "' would be created");
        return true;
    }

    public boolean deleteProtectedRegion(Player player, String regionName) {
        // Placeholder - WorldGuard integration not fully implemented
        // TODO: Implement proper WorldGuard 6.x integration
        plugin.getLogger().info("WorldGuard region deletion not fully implemented - region '" + regionName + "' would be deleted");
        return true;
    }

    public List<String> getRegionsAtLocation(Location location) {
        // Placeholder - WorldGuard integration not fully implemented
        // TODO: Implement proper WorldGuard 6.x integration
        return new ArrayList<>();
    }

    public boolean isLocationInRegion(Location location, String regionName) {
        // Placeholder - WorldGuard integration not fully implemented
        // TODO: Implement proper WorldGuard 6.x integration
        return false;
    }

    public boolean playerCanBuildInRegion(Player player, String regionName) {
        // Placeholder - WorldGuard integration not fully implemented
        // TODO: Implement proper WorldGuard 6.x integration
        return true; // Allow building for now
    }
}